#!/usr/bin/env python3
"""
步骤执行节点

负责执行当前测试步骤
"""

import base64
from datetime import datetime

from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import AzureChatOpenAI

from data.State import DeploymentState
from tools.screenshot_utils import take_screenshot


def execute_current_step_node(state: DeploymentState) -> DeploymentState:
    """
    Execute the complete test case - let model see all steps and decide execution flow
    """
    # Check if already completed
    if state.get("completed", False):
        print("✓ Test case already completed")
        return state

    # Save screenshot before execution
    before_screenshot = state["current_page"].get("screenshot", "")

    try:
        # Execute using enhanced get_location with full context
        state = enhanced_get_location(state)

        # Check if execution was successful
        execution_success = True
        if len(state["history"]) > 0:
            last_record = state["history"][-1]
            if last_record.get("status") == "error":
                execution_success = False

        # Add execution record
        execution_record = {
            "action": "test_case_execution",
            "before_screenshot": before_screenshot,
            "execution_success": execution_success,
            "timestamp": datetime.now().isoformat(),
            "total_steps": len(state.get("task_steps", [])),
            "completed_steps": state.get("completed_steps", 0)
        }

        state["history"].append(execution_record)

    except Exception as e:
        print(f"❌ Error executing test case: {str(e)}")
        # Add error record
        error_record = {
            "action": "test_case_execution",
            "error": str(e),
            "status": "error",
            "timestamp": datetime.now().isoformat()
        }
        state["history"].append(error_record)

        state["step_failed"] = True
        state["retry_count"] += 1

    return state


def build_complete_test_case_messages(state: DeploymentState) -> list:
    """
    构建包含完整测试用例上下文的消息列表
    """
    messages = []

    # 获取测试用例信息
    test_case_name = state.get("test_case_name", "未知测试用例")
    test_case_description = state.get("test_case_description", state.get("task", ""))
    expected_result = state.get("expected_result", "")

    # 构建完整的测试用例上下文
    system_instruction = f'''
## 角色
你是一个安卓测试用例自动化执行助手，你主要负责安卓软件'TT语音'的UI自动化测试。
你能够看到完整的测试用例步骤和期望结果，请根据当前界面内容及状态决定下一步动作。

## TT语音主要功能
1.实时语音开黑：
- 支持多人语音房间，适用于王者荣耀、和平精英、英雄联盟等热门游戏；
- 自动匹配队友，开黑时自动连麦。
  
2.社交房间/语音聊天室：
- 用户可以创建语音房间，进行聊天、K歌、相亲、交友等活动；
- 提供“电台”、“陪玩”、“唱歌”等丰富的语音互动玩法。

3.语音变声器与特效：
- 多种语音变声（如萝莉、大叔、机器人等）；
- 增加趣味性和个性化体验。

4.公会系统与赛事组织：
- 支持游戏公会管理，成员可在TT语音中组织赛事或日常语音活动；
- 适用于公会团队协作、管理、通知等。

5.陪玩/找队友/开黑匹配：
- 用户可发布自己擅长的游戏技能，或找人一起组队打游戏；
- 一些服务有付费机制（陪玩）。

## 测试用例信息
- 用例名称: {test_case_name}
- 用例步骤: {test_case_description}
- 期望结果: {expected_result}

'''
    system_instruction += f'''
    
## 动作列表
当前手机的尺寸为：1200x2670
其中(x1, y1)这种数组为坐标点，代表了再图片中的位置

1.click(start_box='<|box_start|>(x1, y1)<|box_end|>')
  作用：模拟用户点击界面元素
  典型场景：
  - 点击“我”页面 → 进入个人资料编辑页
  - 点击“语音房间” → 加入/创建房间
  - 点击“搜索”按钮 → 查找好友/房间
  -点击“开始游戏”、“匹配队友”、“发送消息”等按钮
2.long_press(start_box='<|box_start|>(x1, y1)<|box_end|>')
  作用：模拟用户长按行为
  典型场景：
  - 长按语音房中的某位用户头像 → 弹出踢人、禁言等管理选项
  - 长按聊天气泡 → 弹出复制、举报、删除等菜单
  - 长按发送键 → 触发语音输入功能
3.drag(start_box='<|box_start|>(x1, y1)<|box_end|>', end_box='<|box_start|>(x3, y3)<|box_end|>')
  作用：模拟滑动操作
  典型场景：
  - 拖动语音消息到垃圾桶 → 删除消息
  - 滑动好友列表 → 查找指定用户
  - 滑动个人资料页 → 查看签名、战绩等扩展信息
  - 左右滑切换“推荐”、“附近”、“交友”页签
4.type(content='')
  作用：模拟输入内容。
  典型场景：
  - 输入昵称、签名、房间名等资料内容
  - 搜索好友、频道或游戏
  - 在聊天室或语音房中输入文字消息
  - 编辑举报理由、反馈内容
5.back
  作用：模拟 Android 返回键，退出当前页面或弹窗。
  典型场景：
  - 从房间详情页返回房间列表
  - 退出个人信息编辑页
  - 关闭设置界面或窗口
  - 取消邀请或关闭弹窗提示
6.wait()
  作用：等待一段时间，用于页面加载或动画完成。
  典型场景：
  - 等待房间列表加载完成
  - 等待语音消息发送成功
  - 等待页面跳转动画结束
  - 避免过快操作导致页面未响应
7.finished(content='xxx')
  作用：表示当前测试用例已完成，输出总结信息。
  典型场景：
  - 当你认为整个测试用例已经完成时，调用finished()
  - finished()必须包含一个字符串参数，用于总结测试用例的执行情况    

## 输出格式
```
Thought: ...
Action: ...
```

## 执行指导
- 仔细阅读测试用例内容，理解测试目标和期望结果，仔细理解图片内容及状态
- 你可以看到完整的测试用例步骤，请根据当前界面状态决定下一步操作
- 不需要严格按照步骤顺序执行，可以根据界面状态灵活调整
- 当你认为整个测试用例已经完成（达到期望结果）时，调用finished()
- 在Thought中分析当前界面状态，判断应该执行哪个步骤或操作
- 如果页面情况不清晰明确时，无法确定下一步操作时，优先使用back返回上一层，直到主页为止

## Note
- `Thought`使用中文进行思考
- 分析当前界面状态，判断测试用例的执行进度
- 根据完整的步骤列表和期望结果，决定下一步最合适的操作
- 当页面存在弹窗时，优先将弹窗关闭
- 只有当整个测试用例完成时才调用finished()

'''

    messages.append({
        "role": "user",
        "content": system_instruction
    })

    # 添加执行历史 - 包含所有相关的执行记录
    history = state.get("history", [])
    execution_records = [r for r in history if r.get("action") == "enhanced_get_location" and r.get("model_response")]

    for record in execution_records:
        if record.get("model_response"):
            messages.append({
                "role": "assistant",
                "content": record["model_response"],
            })

    return messages


def enhanced_get_location(state: DeploymentState) -> DeploymentState:
    """
    Enhanced version of get_location with comprehensive context
    """
    from tools.action_agent import execute_simple_action
    from openai import OpenAI

    # Initialize model
    client = OpenAI(
        base_url="http://10.65.230.19:8005/v1",
        api_key="aaa"
    )

    # 每次循环都获取最新的截图
    screenshot_path = take_screenshot(
        device=state["device"],
        app_name="deployment",
        step=state.get("current_step", 0)
    )

    # 更新当前页面截图
    state["current_page"]["screenshot"] = screenshot_path

    try:
        with open(screenshot_path, "rb") as f:
            image_content = f.read()
        image_data_base64 = base64.b64encode(image_content).decode("utf-8")
    except Exception as e:
        print(f"❌ Failed to read screenshot: {str(e)}")
        state["history"].append({
            "action": "enhanced_get_location",
            "error": f"Failed to read screenshot: {str(e)}",
            "status": "error"
        })
        return state

    messages = build_complete_test_case_messages(state)

    try:

        model = AzureChatOpenAI(
            deployment_name="gpt-4o",
            azure_endpoint="https://east-us-quwan-yw-infra-01.openai.azure.com",
            openai_api_version="2024-02-15-preview",
            openai_api_key="********************************"
        )

        prompt = ChatPromptTemplate.from_messages(
            messages=messages
        )

        chain = prompt | model

        output = chain.invoke({})
        print(output.content)

        # chat_completion = client.chat.completions.create(
        #     model="ByteDance-Seed/UI-TARS-1.5-7B",
        #     messages=messages,
        #     top_p=None,
        #     temperature=0.2,
        #     max_tokens=8192,
        #     stream=True,
        #     seed=None,
        #     stop=None,
        #     frequency_penalty=None,
        #     presence_penalty=None
        # )
        # response = ""
        # for message in chat_completion:
        #     if message.choices[0].delta.content:
        #         response += message.choices[0].delta.content

        model_response = output.content
        print(f"Model response: {model_response}")
        action_line = ""
        for line in model_response.split('\n'):
            if line.strip().startswith('Action:'):
                action_line = line.replace('Action:', '').strip()
                break

        if action_line:
            # Check if this is a finished action
            if action_line.startswith("finished("):
                print(f"✅ Test case completed: {action_line}")

                # 标记整个测试用例完成
                state["completed"] = True
                state["execution_status"] = "completed"
                state["step_failed"] = False
                state["retry_count"] = 0

                # Add completion record
                state["history"].append({
                    "action": "test_case_completed",
                    "model_response": model_response,
                    "completion_action": action_line,
                    "total_steps": len(state.get("task_steps", [])),
                    "completed_steps": state.get("completed_steps", 0),
                    "status": "completed",
                    "timestamp": datetime.now().isoformat()
                })

                print(f"🎉 Test case execution completed successfully!")
                return state

            # Execute action using simple action executor
            action_result = execute_simple_action(action_line, state["device"])

            print(f"✓ Action executed: {action_line}")
            print(f"⏳ Continuing test case execution until finished() is called...")

            # Add to history
            state["history"].append({
                "action": "enhanced_get_location",
                "model_response": model_response,
                "parsed_action": action_line,
                "action_result": action_result,
                "total_steps": len(state.get("task_steps", [])),
                "completed_steps": state.get("completed_steps", 0),
                "status": "success" if action_result.get("status") == "success" else "error",
                "timestamp": datetime.now().isoformat()
            })
        else:
            print(f"❌ Failed to extract action from: {model_response}")
            state["history"].append({
                "action": "enhanced_get_location",
                "model_response": model_response,
                "error": "Failed to extract action",
                "status": "error",
                "timestamp": datetime.now().isoformat()
            })

    except Exception as e:
        print(f"❌ Error in enhanced_get_location: {str(e)}")
        state["history"].append({
            "action": "enhanced_get_location",
            "error": str(e),
            "status": "error",
            "timestamp": datetime.now().isoformat()
        })

    return state
